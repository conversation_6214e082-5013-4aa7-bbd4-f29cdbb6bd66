Curriculum FAQ
"Why do we start with C?"
You're learning how to learn, and learning how things truly work under the hood. This makes you much better at higher level languages.
Not to mention, it impresses people.  
"Why aren’t we learning React in Foundations?"
There isn’t React in Foundations because you can’t start JS by React - it’s too complex, it’s too hard. We are building foundations here, not a framework. JS concepts are already hard enough, no need to add another level of complexity.
Thankfully, this foundation and learning how to learn is giving you the tools to learn whatever framework or language you wish in the future.  
"Why do we learn Python?"
It’s a OOO language, simple, clean, easy to set up, AND can run in almost every platform with “low cost” (CPU/Memory). It’s also used for web apps, scripts, etc. 
Sure, there are other great languages, but again this is about learning fundamental concepts. Once you learn these things, it’ll be easy to pick up other languages and frameworks in the future. 