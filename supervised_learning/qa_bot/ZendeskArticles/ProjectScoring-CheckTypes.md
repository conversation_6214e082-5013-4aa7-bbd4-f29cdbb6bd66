Project Scoring - Check Types
There are three different types of checks:
Requirement checks: These checks exist to make sure that the task fulfills the requirements of the project (README.md exists, the file is executable, banned functions are not used, etc.). They are displayed with red for failure and green for passing and do not have a solid outline. They can potentially block the checking of remaining checks.
=> If you are not passing a requirement check, look to the requirements of the project/task to see what might be wrong. Make sure that everything GitHub related is correct.
Output checks: These checks exist to make sure that the code is performing as is should. They are displayed with red for failure and green for passing and have a solid outline. 
=> If you are not passing an output check, talk with peers to see if you are getting the right output or are missing various edge cases.
Efficiency checks: These checks exist to make sure that the task is performing at the correct efficiency. They are displayed as orange for failure and purple for passing.
=>If you are not passing an efficiency check, try thinking of other algorithms that can increase the efficiency of your code.