Hack Day Overview
Hack Days are a great way to harness all your new skills as full-stack software developers into projects that are perfect to showcase in your portfolio!
 
At Holberton, Hack Days are mandatory mini Hackathons encountered in the third trimester of the Foundations curriculum where your cohort is assigned groups to build a product/service based on project specifications.  Group sizes and length vary by Hack Day.
 
Hack Day teams and projects are released in the Intranet. Projects could be a competition between groups on campus or between Holberton campuses. 
 
Some past challenges include using data from a specific API in a new service, creating a game in multiple languages, and utilizing an internal Holberton API endpoint to create new services for Holberton students. Challenges are updated periodically, let us know if you have an idea for a Hack Day for future cohorts!
 
Happy Hacking!