Curriculum FAQ
"Why don’t we do bigger projects in Foundations?"
We know that having bigger projects in your portfolio has a lot of value, but you need to learn to code first. The foundations are just that - building the foundations of your future endeavors. We will have some bigger projects along the way including your portfolio project and Hack Day projects, but we highly recommend you plan to build most side projects only if and when you feel mastery in all the curriculum’s mandatory topics and the expectations of a peer learning environment. 
"Why don’t we do any mobile (or Machine Learning or Advanced web stack etc) during Foundations?"
Foundations are just the beginning; there is so much to learn still, but we can’t possibly fit everything into a nine-month period. That’s why we offer Specializations : ) 
"There are no clear applications to what we are learning; printf and Shell are not practical. What’s up with that?"
If you don’t see applications to what you are learning, make sure to reach out to a peer, TA, prior cohorts, or staff. It’s important to understand what you are doing and why. If you aren’t, it means you should return to the drawing board to gain context. 
That said, somethings may not seem relevant at the time, but the more you learn, follow The Framework, and progress, you will see how it all ties together.