Text Editor Overview
At Holberton, you have the choice of using either <PERSON><PERSON><PERSON> or Vim as your text editors. Other text editors or IDEs are not allowed for your project work. 
Emacs Tips
Practice Emacs Shortcuts 
Setting up Emacs to work with `betty` coding style
Indentation for Python: Add to your `.emacs` file
;; fix for python indent bug(add-hook 'python-mode-hook  (lambda () (setq python-indent-offset 4)))
Vim Tips
Practice Vim Shortcuts 
Setting up Vim to work with `betty` coding style
As an engineer, you may find yourself in an environment where you cannot install an IDE and the only editors available to you are Emacs or Vim (especially in sysadmin / devops!). Knowing how to utilize either of these editors gives you more flexibility as an engineer. You can use whatever text editor / IDE you like outside of Holberton, but within the context of school projects, you must use Emacs or Vim.