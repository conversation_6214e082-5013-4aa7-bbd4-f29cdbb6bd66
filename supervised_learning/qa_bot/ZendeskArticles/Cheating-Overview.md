Cheating of any kind goes against the spirit and purpose of your time here at Holberton, and will absolutely not be tolerated. Taking the work of others and presenting it as your own not only reflects poorly on you as a student but on Holberton as an institution. If you are caught cheating (we have an extremely good system for identifying plagiarism), you will be asked to meet with staff and potentially face expulsion.
If you follow the Holberton Framework, are patient and kind to yourself, and put in the time, you will be successful without resorting to plagiarizing the work of others. Here are a couple of points to keep in mind when working on your projects:
You should not look at others’ code (either locally or on the web) for inspiration until after you have completed the work yourself, and you are certain that you understand the concepts outlined in the task/project. 
If you read it before writing your own version, not only are you cheating, but you will lock yourself into thinking about the problem in the way you have already seen it done. Parts of the other person’s code will likely stick in your memory and wind up in your own codebase, which qualifies as plagiarism.