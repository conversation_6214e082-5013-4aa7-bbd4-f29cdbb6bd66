Evaluation Quiz Scoring
The score is computing like this:
1pt for correct answer
0.5pt for "I don't know" answer
0pt of wrong answer
The system counts the number of seconds the student spends outside the quiz page. For each 10 seconds outside of the quiz, the students’ final quiz grade will be lowered by 1%. 
If the quiz is started outside the window when the Software Engineer releases the quiz, the maximum score of the quiz is lowered. For example, if the Software Engineer releases the evaluation quiz at 3:15pm and a student begins the quiz at 4:17pm, the student’s maximum possible score is 65% because it wasn’t started in the allotted hour time. If the quiz is done the day after it is released, the maximum score available is 60%.
If you cannot take the quiz for some reason, please contact the Software Engineer.