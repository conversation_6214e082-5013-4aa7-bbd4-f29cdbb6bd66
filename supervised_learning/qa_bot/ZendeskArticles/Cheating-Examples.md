Examples of Cheating:
You directly copy any line of code from another student or code seen on Github, a website, or text.
You use another person’s source code for reference while working on your own.
You watch another student write code and then go and write your own, and vice versa.
Another student tells you how you should write your code and vice versa.
You use open source software in your project and don’t cite it in your readme (this shouldn’t come up in the vast majority of the Foundations curriculum). 
How to Collaborate without Cheating:
Discuss the day’s readings/concepts with your peers in order to deepen your understanding.
Discuss conceptual approaches to a project, e.g. “I think a recursive approach to this problem would add too much time complexity”.
Whiteboard, whiteboard, whiteboard. Feel free to present your approach to a problem on a whiteboard. This does not mean directly writing out your code. Use diagrams and pseudocode to share your approach to a problem.
If you get stuck, follow the Framework.
Make sure that once you’re ready to code, you find a space where you won’t find yourself tempted to glance at another student’s screen or talk about what you’re writing.