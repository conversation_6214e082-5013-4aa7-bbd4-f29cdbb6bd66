Events - Hackathons
Hackathons are an industry-wide tool for rapidly developing software to tackle a specific challenge, and are one of the best ways to foster learning. The somewhat competitive and rapid nature of hackathons forces students to learn at a pace they might not otherwise impose on themselves. Hackathons can range from several hours to several days. The typical idea is that participants split into teams up to a capped number of people, are given a problem to fix with technology, and then given free rein to pursue the challenge as they see fit. One important thing to note is that if students register ahead of time, they should not decide at a later time that they do not wish to participate. Hackathons are a commitment, and that commitment should be taken very seriously. It would reflect very poorly on the student and on Holberton as an institution if people start to back out of these sorts of commitments.