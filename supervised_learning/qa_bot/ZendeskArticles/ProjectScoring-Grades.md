Project Scoring - Grades
Each project has a weight, which can be found in the top left corner of the project page. This weight acts as the number of times the project score gets counted towards the student’s overall grade. Most projects have a weight of 1, but larger projects (like printf, shell, etc.) will have a higher weight. 
Example: If you completed 5 projects at 100%, each with a project weight of 1, and 1 project at 50% with a project weight of 5, your grade would be calculated as follows:
Grade = (100% + 100% + 100% + 100% + 100% + 5*50%) / (1 + 1 + 1 + 1 + 1 + 5) = 75%