Technical Writing Overview
Technical writing is an invaluable skill and an excellent way to articulate and share your knowledge. It's not enough to just be able to code; being able to explain the concepts behind the code proves deeper understanding as well as the ability to communicate with others.
Technical Writing Basics
You will write several blogs during your time as a student. Technical writing pieces are included in both project assignments and as extracurricular assignments from staff. 
The general requirements for all blogs are as follow:
Have at least one picture at the top of the blog post
Publish your blog post on Medium or LinkedIn
Share your blog post at least on LinkedIn
Write professionally and intelligibly
Explain every step/concept you know, and give examples -- a total beginner should understand what you have written
Please remember that these blogs must be written in English to further your technical ability in a variety of settings
Remember that future employers will see your articles; take your writing seriously and produce something that will be an asset to your future
Blogs are manually reviewed by a staff member, a ST, or a peer and are evaluated on how well they meet the above requirements as well as specific requirements based on topic, which is listed in the associated project.
This should go without saying, but plagiarism is unacceptable.