Project Scoring - GitHub
Auto QA reviews will be completed by accessing students’ code from GitHub. Students should make sure that they only use the GitHub account listed in their intranet profile. If a student’s GitHub username is misspelled or their project is uploaded to a different account, the project will not be properly corrected. If the project repository and/or task files are not located/spelled in accordance with the project description, the project will not be properly corrected. Projects will not be re-graded for these issues.  
If a project is completed in teams, the project will be scored by selecting the first locatable repository amongst the team members. It is for this reason that it is highly recommended that these projects be completed using only one repository. Creating a duplicate repository with the same name or forking of a repository with the same name before all deadlines have passed may cause issues if there are discrepancies between repositories. Projects will not be re-graded for these issues.