Scoring Overview
Projects are scored based on the performance of tasks. A task has multiple checks, each with their own point value. All checks are pass/fail, meaning a passed check will receive full points while a failed check will receive 0 points. The project grade will then be calculated based on the number of points the student earns out of the total points available.
Example: A project has two tasks. The first task has 5 checks, each worth 1 point. The second task has 3 checks worth 1, 1, and 3 points, respectively. You pass all checks except for the last check in the second task. Your score would be: 
Overall Score = 100% * Points Earned / Total Points Available
Overall Score = 100% * 7 / 10 = 70%
QA Reviews
Tasks are scored using two methods: Auto or Manual QA review. Auto QA reviews are automatically run at midnight after the project’s first deadline. Manual QA reviews, however, must be completed by staff or a peer who has already been reviewed. Manual QA reviews must be completed within 1 week of the first deadline, otherwise, 0 points will be given for all tasks requiring such a review. It is the student’s responsibility to ensure that Manual QA reviews are completed before the deadline. If a project has a Manual QA review component, the overall project grade will not be released until after the Manual QA review is completed. 
Once a task’s QA review is complete, a button labeled “QA Review” will appear below the task. Clicking this button will display a full description of all the task’s checks and reasons that a student passed/failed each check. Some checks include the exact main file used to check students’ code, which can be used to help understand and fix mistakes.