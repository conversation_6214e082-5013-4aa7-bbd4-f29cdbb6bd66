<PERSON>ck Interview Overview
Mock Interviews are multi-purpose:
They help you train for technical interviews
They help you and the staff understand your technical skills and general knowledge
Mock Interviews are mandatory. If you are unable to be present for the entire duration, you must take a PTO via the intranet in advance.
Why do we do Mock Interviews and why are they mandatory?
Technical interviews for software engineering positions are draining -- intellectually, emotionally, and physically. Many of them can last upwards of 8 hours with few or no breaks. It's not enough for you to "know the answers" to their questions; you need to be able to clearly communicate your thought process and understanding despite pressure, stress, and exhaustion. 
This type of stamina is not developed overnight; it requires consistent practice.
Holberton School has implemented regular, increasingly difficult mock interviews to simulate the technical interview so that when your time comes you will be ready! That being said, it will only be as effective as your willingness to take it seriously, and provide one another with supportive, informed feedback.