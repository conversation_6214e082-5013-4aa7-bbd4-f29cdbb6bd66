Standard Projects
Most projects at Holberton are Solo projects, where you rely on your research and coding skills to complete tasks. 
These projects are oftentimes - though not exclusively - shorter in duration, and have a "standard" task/solution format. Their progression is usually in order of complexity - ending with advanced questions. Advanced tasks, however, are supplemental and complementary to a given mandatory project; the points are bonus and you cannot access the advanced questions without first completing the mandatory ones. The Checker is usually released halfway through the duration of the project, with some exceptions.
Group
Group projects are those where more than one student contributes to the code. Usually, these are pair projects, with either the Intranet choosing your partner or you choosing, depending on the project. 
 
All group projects are graded in relation to contribution equality. This means that the closer to an even split the partners' contributions are, the higher their composite score will be. The inverse leads to a lower score. 
Blogs
Technical writing is an incredibly important component of the curriculum and professional preparation offered at Holberton. Starting from the very beginning, you are required to write technical articles. 
These blogs are peer-reviewed and you are responsible for ensuring that your blog post is reviewed before the deadline. If no peer is able to review your blog, you should ask the TA or software engineer to review it. 
Hack Days
Refer to Hack Days article in this knowlege base.
 
Portfolio Projects
Refer to the Portfolio Project section of this knowledge base.