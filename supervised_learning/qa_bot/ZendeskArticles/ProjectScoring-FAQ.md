Project Scoring FAQ
"My code gives the correct output, but I’m failing all of the checks!"
Did you make sure to check that you have all the requirements? GitHub setup correctly, README.md exists and is not empty, code compiles, files are executable, files are spelled correctly, code does not contain forbidden variables/functions/conditionals etc.
"Can I ask for a new correction more than once?"
Yes, you can ask for a new correction as many times as you would like.
"My grade went down after the second deadline. What happened?"
It is possible to lose points on the second regrade if a task that you did not complete is passing fewer checks than before. Check the QA review for more details as to how your tasks were graded and where each point comes from. 
"Does the team contribution score get updated for the second deadline?"
Yes, team contributions are tracked through the second deadline.
"What happens to the contribution score after the second deadline?"
After the second deadline, students are free to fork their team’s repository and work alone on updating their project for the 50% regrade without negatively impacting their contribution score. However, the contribution score will remain.