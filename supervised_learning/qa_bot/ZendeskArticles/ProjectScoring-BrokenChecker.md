Project Scoring - Broken Checker
“The checker is broken”
99% of the time, the checker is not broken. Remember, hundreds of students have gone through this program! Most of the notifications staff receive about the checker being broken are due to students having forgotten a README.md, not making their files executable, misspelling something, not returning the correct value, or missing edge cases. Use your knowledge of what types of checks you are failing to help guide you (see Check Types). If you think the checker is broken:
Check that your code meets ALL requirements
If that doesn’t fix the issue, follow the Framework in its entirety:
Ask peers
Ask more peers
Ask previous students
Ask your TA
Ask more TAs
If you have exhausted the framework, put together a hypothesis as to why you think the checker is broken and test it! 
If you still think something is wrong, please post your issue in the #checker_issues slack channel. Make sure to include a link to the project, the task number, and why you think the checker is broken.