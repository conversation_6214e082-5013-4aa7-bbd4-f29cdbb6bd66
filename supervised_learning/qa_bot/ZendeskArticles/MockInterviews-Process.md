Mock Interview Process
Interviewer:
The interviewer’s job is to simulate an interview experience and to provide fair input and direction without just giving the answers away. 
The interview should ensure that the student is continuously commenting as they whiteboard/code and not coding silently -- you should seek to understand the candidate's problem-solving process.
The interviewer is encouraged to ask questions in order to make sure the concept is well understood.
The interviewer is allowed to help the candidate if they cannot complete the challenge.
The interviewer should make sure edge cases are correctly handled. 
Candidate:
The candidate is to push themselves to articulate their thought process, whiteboard, ask great questions, and manage their time effectively.
The candidate needs to comment on everything they do and explain their thought process. Don't code in silence, an interviewer wants to see how you problem solve.
The candidate can choose to code on the computer or whiteboard; if the latter is chosen, they still must input their code to verify that it runs.
The candidate must prove that their code works by using different examples and/or inputs.
The candidate is allowed to ask the interviewer questions.
The candidate does not have to commit to Github.