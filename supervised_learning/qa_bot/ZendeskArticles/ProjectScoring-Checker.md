Project Scoring - The Checker
Holberton’s Correction System (AKA The Checker) is used to score all Auto QA reviews. For tasks requiring an Auto QA review, a “check my code” feature may be available. (Note: check my code may be released only part way through the project or after the first deadline) This feature allows students to check if their code is passing all of the checks for the task in question. This feature is not meant to tell students what is wrong with their code but is merely there to help in determining if their code is passing the checks used to grade their project. The checks are randomized between students and change every 24 hours.