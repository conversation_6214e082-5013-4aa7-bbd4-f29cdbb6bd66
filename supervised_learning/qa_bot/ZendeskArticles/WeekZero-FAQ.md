Week Zero FAQ
"Do I need to be on-site for Week Zero?"
Yes, all Week Zero days are mandatory and you will miss critical information as well as the opportunity to meet your peers if you are not here. This is a physical school and all of our methodology's benefits only apply if you are physically present.
"The first week has been so overwhelming, I don't think I can keep up with the program."
The program is intense and students hit the ground running from their very first day on site. However, you're not in this program alone; you have a cohort of peers and a network of alumni and professional advisors here to help you along your way. If you're struggling, please reach out so that we can support you!
"Why do I have to use Emacs/Vim, why can't I use an IDE like VSCode or Atom?"
As an engineer, you may find yourself in an environment where you cannot install an IDE and the only editors available to you are Emacs or Vim. Knowing how to utilize either of these gives you more flexibility as an engineer. You can use whatever text editor / IDE you like outside of Holberton, but within the context of school projects, you must use Emacs or Vim. (See: Text Editors)