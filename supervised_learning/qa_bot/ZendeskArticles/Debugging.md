Debugging Overview
Debugging is the process of finding and fixing errors in software that prevents it from running correctly. As you become a more advanced programmer and an industry engineer, you will learn how to use debugging tools such as `gdb` or built-in tools that IDEs have. However, it's important to understand the concepts and processes of debugging manually.
Debugging Basics
There are several methods of debugging:
`printf` or `print` statements
reading the error messages
rubber duck debugging
method of elimination
making incremental changes and testing
checking misspellings, whitespace, typos
checking order of operations/comparisons
If you have reached the point in the program where you are utilizing malloc/free and writing longer programs, you may find these resources on Valgrind and `gdb` useful: 
Valgrind
gdb
Debugging FAQ
"Why do I have to debug manually? Debugging tools make it faster."
As a working engineer, using tools will be beneficial for more complex bugs in shared codebases. But as a student, you must take this time to really understand your code and what it's doing and how to fix it, rather than rely on a tool to tell you what's wrong. This will make you a more efficient engineer in the long run.
Additionally, debuggers won't always tell you exactly what's wrong. You must be able to combine your own debugging skills alongside other tools to solve problems.