Curriculum Overview
Believe it or not, the content, delivery, methodology, ordering, and occasional vagueness of the curriculum is by design. <PERSON><PERSON><PERSON> has a deep commitment to providing high-quality education; we do not just want to help you understand something in a given project - we want you to have the tools to continue to learn for life. 
We want you to learn how to learn. Regardless of the language, framework, or company, we want you to have the soft and technical skills to set you apart in your professional endeavors.  
The foundational curriculum was created by people with prestigious computer science degrees and combined *decades* of experience in the industry at top tech companies. 
They had noticed aspiring software engineers would devote years to studying, sometimes accumulating over a hundred thousand dollars of student debt, and would still not have the skills necessary to find a job in software engineering. <PERSON><PERSON><PERSON><PERSON> and <PERSON> decided to build a new school; one that taught students to think and learn like the best programmers, one that helped students develop soft skills to get them noticed in interviews and throughout their careers, and one with a curriculum developed to give students practical experience through a Full-Stack engineering program.
We know the program will oftentimes not feel easy; this is to be expected! It doesn’t mean you don’t belong or are not well-suited to be an engineer. It means that by using The Framework in a safe space of learning you can develop areas in your technical, professional, and personal life that will serve you in the years to come. 
Remember, no matter how tough it can feel, know that we have your back and we’re standing nearby to jump in when you need support and help.