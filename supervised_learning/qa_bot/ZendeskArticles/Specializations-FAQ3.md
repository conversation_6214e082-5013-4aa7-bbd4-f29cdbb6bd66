Specializations FAQ
 
“How does <PERSON><PERSON><PERSON> measure the quality of the curriculum?” 
The way we measure quality is ensuring it’s relevant to the industry, and that the students who complete it in good standing will be prepared for employment in that respective field. 
 
We haven’t measured the exact amount of time dedicated to each project’s creation, iteration, or review, since this would be prohibitively ineffective in regards to agile development. We can, however, provide a few examples to share context: In the case of Full-Stack Web Development, the 9 months of content are comprised of 928 hours of senior contribution and 1050 hours of Curriculum Team implementation. 
 
Qualitative examples include, but are not limited to, how the AR/VR curriculum has a stamp of approval from Unity, one of Machine Learning’s core advisors also serves on the Deep Learning & AI Technical Committee of NASA’s Frontier Development Lab, and that the Low Level Specialization was co-created by <PERSON> valedictorian from the European Institute of Technology with career history as a Software Engineer, Product Manager, and a CEO in different tech companies.
In the Spring of 2020, we’re committing to sharing a curriculum review schedule with students - including the profiles of who participate - so you have heightened visibility of how we ensure quality.