Technical Writing FAQ
"Why are the blog instructions so vague? How do I know what to write about?"
We do not outline all the requirements because part of these challenges is to teach/learn/experience how to find what needs to be said on a topic that's new to you. You may not know what needs to be explained to a reader, but your research on the topic should help guide you. 
"If the blog should be understood by beginners, why is there an expectation of having in-depth explanations, even to the point of using jargon beginners may not understand?"
Even if your audience is beginners, we must trust that they could understand a highly technical topic if it is explained well. Be sure to not only use appropriate jargon but to define the jargon you use. After reading your blog, the hope is that your audience will know the technical topic at the deepest level possible.
“My reviewer scored me more strictly than others. Can’t we make this more standardized?”
Each student is entrusted to score fairly and to the best of their ability. We understand that there may be a variance between how reviewers score, but it also reflects the interpretation and understanding of a general reader audience. 
"Can I write other technical blogs outside of school projects?"
Of course! You're encouraged to share them with staff and peers as well. If you're interested in writing more but don't know what to write about, here are some ideas:
Keeping a devblog for big projects is a great way to document processes, successes, failures, and to-dos.
If you find a framework, SDK, API, etc. has limited or confusing documentation, write a tutorial to help others with the problems that you've encountered.