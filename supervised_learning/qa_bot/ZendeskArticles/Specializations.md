Specialization Overview
After you acquire the foundations of software engineering, you specialize.
Students have the opportunity to go beyond the foundations with specializations that focus on exciting emerging technologies. After acquiring the foundations, you will be able to choose from our current specializations: Machine learning,  AR/VR, Low-Level and algorithms or Full-Stack Web Development.

Format
Still no teachers or formal lectures
Students are learning by creating with peers
Opportunity for Cross-Campus Collaborations 
Monthly Streamed Topic Deep Dives from Content Creators and Industry Experts
 
Similarly to Foundations, the first 6 weeks of the Specialization will have about 2 Peer Learning Days a week which phase to weekly. Mock Interviews take place approximately every 6 weeks. 
Specializations allow students to collaborate and partner on projects with peers at other campuses - leveraging global peer-learning and development - technically and socially. Topic deep dives and PLDs will also incorporate cross-campus interactions. 
Although attendance and campus participation is highly encouraged, there are no PTOs for Specializations. Instead, accrued participation in Mock Interviews and Peer Learning Days results in interview practice with staff & advisors. 

To stay sharp and well-rounded, each week of your Specialization you receive an Interview Algorithm (which is mandatory and contributing towards your trimester and overall average); they ramp in complexity throughout the program, so you're ready to put your best foot forward in interviews.

You will not be pushed to apply to any positions, instead this focuses on technical preparation, interviewing skills, and portfolio polishing. 