Live coding sessions are a great way to review and reinforce knowledge of concepts and coding techniques. It is also the occasion to hear and follow the thought process of a software engineer and learn from it.
Even though they are called live “coding”, these sessions might take different formats:
Review of specific concepts
Q&A
Live coding
White boarding
Code reviews
Etc.
Or any combination of the above.
Live coding sessions are optional. They are added at the discretion of the staff and are not part of the official curriculum of Holberton.
There are no recordings of the live coding sessions shared after the sessions' end. We do not authorize students to record live sessions without prior written consent from the staff and every single participant.
We want to encourage all the students to join these sessions and interact with other students and staff. Live sessions have great advantages, including interaction and communication that are not possible with recordings. Being able to ask questions about your specific challenges is a big advantage.