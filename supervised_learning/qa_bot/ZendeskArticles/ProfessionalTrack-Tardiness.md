Professional Track - Tardiness
Although students are encouraged and expected to arrive promptly for mandatory day check-ins, <PERSON><PERSON><PERSON> has implemented a forgiveness window in which students may arrive late and not be penalized. In these scenarios, the student must ensure that they talk to the Software Engineer or Teacher Assistant and explain their reasoning for being late. Each campus/region has its own forgiveness window and students should be made aware that this is due to cultural differences.
If students repeatedly arrive late without good reason, the Software Engineer will schedule a meeting to discuss ways in which students can work on their tardiness (e.g. If the student is always late because of the bus, the suggestion would be to wake up 30 minutes earlier). If the students are still tardy without reason thereafter, it will negatively impact their score by 5%. 
Some examples of acceptable tardiness:
"My nanny quit and I had to take my child to school"
"My family member was ill and I had to take them to the doctor"
"There were system-wide transportation outages"
"My car/bike got stolen"
Some examples of unacceptable tardiness:
"I missed my bus/train/cab/ferry"
"There was traffic"
"My alarm didn't go off"
"I didn't know it was a mandatory day"
"I haven't finished my projects"