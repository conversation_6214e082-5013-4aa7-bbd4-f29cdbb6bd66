Curriculum FAQ
"Why aren’t the instructions clearer?"
It’s important to develop the critical thinking ability to sift through incomplete information and secure answers/resolution. It is part of professional life, and it’s definitely part of being a software engineer.
"Why don’t you share the edge cases with me?"
That would be like doing the work for you! Thinking about a problem from all angles is precisely what you’ll be paid for in the years to come ; ) Yes, a company explains some guidelines and requirements, but they will specifically be seeking out team members who can bring analytical skills to the table and thinking thoroughly on a problem and its corresponding solutions. 
"Why can’t I see before the first deadline what the checker is looking for?"
Please, see above 